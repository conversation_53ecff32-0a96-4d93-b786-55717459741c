import config from "./src/config/index.js";
const { imageConfig } = config;

import createNextIntlPlugin from "next-intl/plugin";
const withNextIntl = createNextIntlPlugin();
/** @type {import('next').NextConfig} */

const nextConfig = {
  images: {
    remotePatterns: imageConfig.domains,
    domains: imageConfig.domains.map(d => d.hostname),
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/i,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            icon: true,
          },
        },
      ],
    });

    // Fix for undici private fields issue
    config.resolve.fallback = {
      ...config.resolve.fallback,
      "undici": false,
    };

    return config;
  },
};

export default withNextIntl(nextConfig);
